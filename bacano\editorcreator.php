// Restringir acceso a Administradores y Editores
if ( ! is_user_logged_in() || ( ! current_user_can('administrator') && ! current_user_can('editor') ) ) {
    wp_die('You do not have permission to access this page.');
}

// Verificar si el usuario está logueado
$is_user_logged_in = is_user_logged_in();
$user_has_courses = false;
$my_courses_data = [];

// Si el usuario está logueado, obtener los cursos inscritos
if ( $is_user_logged_in ) {
    $request  = new WP_REST_Request('GET', '/asg/v1/my-courses');
    $response = rest_do_request($request);
    if ( ! $response->is_error() ) {
        $data = $response->get_data();
        if ( ! empty( $data['data']['courses'] ) ) {
            $user_has_courses = true;
            $my_courses_data = $data['data']['courses'];
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Course Management - Ability Seminars Group</title>

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&display=swap" rel="stylesheet">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: 'Outfit', sans-serif; background: #f4f6fa; color: #1a202c; }
    .container { max-width: 80%; margin: 40px auto; padding: 0 20px; }
    h1 { font-size: 2rem; font-weight: 700; margin-bottom: 20px; color: #0C1B40; }

    .controls { margin-bottom: 40px; }
    .category-filters {
      display: flex;
      align-items: center;
      gap: 8px;
      background: linear-gradient(135deg, #0C1B41 0%, #2E5F8A 100%);
      border-radius: 50px;
      padding: 6px 12px;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
    }
    .filter-btn {
      flex: 0 0 auto;
      padding: 8px 16px;
      border: none;
      background: transparent;
      color: rgba(255,255,255,0.9);
      border-radius: 30px;
      cursor: pointer;
      font-weight: 600;
      transition: background 0.3s, color 0.3s;
      white-space: nowrap;
      font-size: 0.9rem;
    }
    .filter-btn.active,
    .filter-btn:hover {
      background: #fff;
      color: #0C1B40;
    }
    .search-input {
      flex: 1 1 auto;
      min-width: 150px;
      padding: 4px 12px;
      border: none;
      border-radius: 28px !important;
      font-size: 0.9rem;
    }
    .search-input:focus { outline: none; }

    .courses-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px,0.3fr)); gap: 40px; }
    .course-card {
      background: #fff;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      height: 100%;
	  max-width: 480px;
      overflow: hidden;
      transition: transform 0.3s, box-shadow 0.3s;
    }
    .course-card:hover {
      transform: translateY(-6px) scale(1.02);
      box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    }

    /* Add New Course Card */
    .add-course-card {
      background: #fff;
      border: 2px dashed #2563eb;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
      max-width: 480px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      padding: 2rem;
    }
    .add-course-card:hover {
      transform: translateY(-6px) scale(1.02);
      box-shadow: 0 8px 20px rgba(37, 99, 235, 0.2);
      border-color: #1d4ed8;
      background: #f8faff;
    }
    .add-course-icon {
      font-size: 3rem;
      color: #2563eb;
      margin-bottom: 1rem;
    }
    .add-course-title {
      font-size: 1.25rem;
      font-weight: 700;
      color: #0C1B40;
      margin-bottom: 0.5rem;
    }
    .add-course-subtitle {
      font-size: 0.9rem;
      color: #6b7280;
    }
    .course-image img { width: 100%; height: auto; object-fit: cover; }
    .course-content { padding: 20px; display: flex; flex-direction: column; flex: 1; }
    .course-title { font-size: 1.25rem; font-weight:700; color: #0C1B40; margin-bottom: 10px; }
    .course-description { font-size:0.9rem; color:#4a5568; margin-bottom:15px; flex:1; }
    .course-actions { display:flex; justify-content:center; }

    .btn-action {
      display: block;
      width: 100%;
      padding: 12px;
      background: #0C1B40;
      color: #fff !important;
      text-decoration: none !important;
      font-weight:600;
      text-align:center;
      border-radius: 4px;
      transition: background 0.2s;
    }
    .btn-action:hover { background: #122962; }

    .loading { text-align:center; padding:60px 0; color:#a0aec0; }
    .loading i { font-size:2rem; animation:spin 1s linear infinite; }
    @keyframes spin { to{transform:rotate(360deg);} }

    /* Modal Styles */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    .modal-overlay.show {
      display: flex;
    }
    .modal-content {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      width: 90%;
      max-width: 500px;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e5e7eb;
    }
    .modal-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: #0C1B40;
      margin: 0;
    }
    .modal-close {
      background: none;
      border: none;
      font-size: 1.5rem;
      cursor: pointer;
      color: #6b7280;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .modal-close:hover {
      color: #374151;
    }

    /* Form Styles */
    .form-group {
      margin-bottom: 1.5rem;
    }
    .form-label {
      display: block;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }
    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid #e5e7eb;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.2s;
    }
    .form-control:focus {
      outline: none;
      border-color: #2563eb;
      box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }
    .btn-primary {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      width: 100%;
    }
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    }
    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    /* Image Upload Styles */
    .image-upload-area {
      border: 2px dashed #d1d5db;
      border-radius: 8px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s;
      background-color: #f9fafb;
      margin-top: 0.5rem;
    }
    .image-upload-area:hover {
      border-color: #2563eb;
      background-color: #eff6ff;
    }
    .image-upload-area.dragover {
      border-color: #2563eb;
      background-color: #eff6ff;
    }
    .image-preview {
      max-width: 100%;
      max-height: 200px;
      border-radius: 8px;
      margin-top: 1rem;
      display: none;
    }
    .upload-text {
      color: #6b7280;
      margin: 0.5rem 0;
    }
    .upload-icon {
      font-size: 2rem;
      color: #9ca3af;
      margin-bottom: 0.5rem;
    }

    /* Modern Tutorial Styles */
    .tutorial-active-element {
      position: relative !important;
      z-index: 1000 !important;
      transform: scale(1.05) !important;
      transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
      box-shadow: 0 20px 40px rgba(37, 99, 235, 0.3),
                  0 0 0 4px rgba(37, 99, 235, 0.2),
                  inset 0 0 0 3px #2563eb !important;
      border-radius: 12px !important;
      animation: tutorialGlow 2s ease-in-out infinite !important;
    }

    @keyframes tutorialGlow {
      0%, 100% {
        box-shadow: 0 20px 40px rgba(37, 99, 235, 0.3),
                    0 0 0 4px rgba(37, 99, 235, 0.2),
                    inset 0 0 0 3px #2563eb;
      }
      50% {
        box-shadow: 0 25px 50px rgba(37, 99, 235, 0.5),
                    0 0 0 6px rgba(37, 99, 235, 0.4),
                    inset 0 0 0 3px #1d4ed8;
      }
    }

    .tutorial-dimmed {
      opacity: 0.3 !important;
      filter: grayscale(80%) blur(1px) !important;
      pointer-events: none !important;
      transition: all 0.6s ease !important;
    }

    .tutorial-click-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60px;
      height: 60px;
      border: 3px solid #2563eb;
      border-radius: 50%;
      background: rgba(37, 99, 235, 0.1);
      z-index: 1001;
      pointer-events: none;
      animation: clickPulse 1.5s ease-in-out infinite;
    }

    .tutorial-click-indicator::before {
      content: '👆';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 24px;
      animation: bounce 1s ease-in-out infinite;
    }

    @keyframes clickPulse {
      0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
      }
      50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.7;
      }
    }

    @keyframes bounce {
      0%, 100% { transform: translate(-50%, -50%) translateY(0); }
      50% { transform: translate(-50%, -50%) translateY(-5px); }
    }

    @keyframes slideUp {
      from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
      }
      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      25% { transform: translateX(-5px); }
      75% { transform: translateX(5px); }
    }

    /* Modern Floating Message */
    .tutorial-message {
      position: absolute;
      top: 20px;
      right: -350px;
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: white;
      padding: 1rem 1.5rem;
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
      z-index: 2000;
      width: 320px;
      transition: right 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .tutorial-message.show {
      right: 20px;
    }

    .tutorial-message-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.5rem;
    }

    .tutorial-message-icon {
      width: 24px;
      height: 24px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }

    .tutorial-message-title {
      font-weight: 700;
      font-size: 0.9rem;
      margin: 0;
    }

    .tutorial-message-text {
      font-size: 0.85rem;
      line-height: 1.4;
      margin: 0;
      opacity: 0.95;
    }

    .tutorial-progress-modern {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      height: 4px;
      margin: 0.75rem 0 0 0;
      overflow: hidden;
    }

    .tutorial-progress-bar-modern {
      background: white;
      height: 100%;
      transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      width: 0%;
      border-radius: 10px;
    }

    .tutorial-step-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.5rem;
      font-size: 0.75rem;
      opacity: 0.8;
    }

    /* Welcome Modal */
    .welcome-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 2000;
    }

    .welcome-modal.show {
      display: flex;
    }

    .welcome-content {
      background: white;
      border-radius: 16px;
      padding: 2.5rem;
      max-width: 500px;
      text-align: center;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    }

    .welcome-icon {
      font-size: 4rem;
      color: #2563eb;
      margin-bottom: 1.5rem;
    }

    .welcome-title {
      font-size: 1.75rem;
      font-weight: 700;
      color: #0C1B40;
      margin-bottom: 1rem;
    }

    .welcome-text {
      color: #4a5568;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .welcome-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
    }

    .welcome-btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
    }

    .welcome-btn-skip {
      background: #f3f4f6;
      color: #6b7280;
    }

    .welcome-btn-skip:hover {
      background: #e5e7eb;
    }

    .welcome-btn-start {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: white;
    }

    .welcome-btn-start:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    }

    @media (max-width: 768px) {
      .category-filters { flex-wrap: wrap; white-space: normal; justify-content: center; }
      .filter-btn { flex: 1 1 45%; margin: 4px 0; text-align: center; }
      .search-input { flex: 1 1 100%; margin: 4px 0; }
      body { padding: 20px; }
      h1 { font-size: 1.5rem; }
      .modal-content {
        width: 95%;
        padding: 1.5rem;
      }
      .tutorial-tooltip {
        max-width: 280px;
        padding: 1rem;
      }
      .welcome-content {
        margin: 1rem;
        padding: 2rem;
      }
      .welcome-buttons {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <div class="container" style="position: relative;">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h1 style="margin: 0;">Course Management</h1>
      <button onclick="restartTutorial()" style="padding: 8px 16px; background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; color: #374151; cursor: pointer; font-size: 0.875rem;" title="Restart Tutorial">
        <i class="fas fa-question-circle"></i> Tutorial
      </button>
    </div>
    <div class="controls">
      <div class="category-filters">
        <button class="filter-btn active" data-category="all">All Categories</button>
        <button class="filter-btn" data-category="finance">Finance</button>
        <button class="filter-btn" data-category="marketing">Marketing</button>
        <button class="filter-btn" data-category="personal-development">Personal Development</button>
        <button class="filter-btn" data-category="technology">Technology</button>
        <button class="filter-btn" data-category="business">Business</button>
        <input type="text" id="searchInput" class="search-input" placeholder="Search courses...">
      </div>
    </div>
    <div id="coursesGrid" class="courses-grid"></div>
    <div id="loading" class="loading" style="display:none;"><i class="fas fa-spinner"></i><p>Loading courses...</p></div>

    <!-- Modern Tutorial Message - Inside container -->
    <div id="tutorialMessage" class="tutorial-message">
      <div class="tutorial-message-header">
        <div class="tutorial-message-icon">
          <i class="fas fa-graduation-cap"></i>
        </div>
        <h4 id="tutorialTitle" class="tutorial-message-title"></h4>
      </div>
      <p id="tutorialText" class="tutorial-message-text"></p>
      <div class="tutorial-progress-modern">
        <div id="tutorialProgressBar" class="tutorial-progress-bar-modern"></div>
      </div>
      <div class="tutorial-step-info">
        <span id="tutorialCounter"></span>
        <span id="tutorialAction">👆 Click to continue</span>
      </div>
    </div>
  </div>

  <!-- Welcome Tutorial Modal -->
  <div id="welcomeModal" class="welcome-modal">
    <div class="welcome-content">
      <div class="welcome-icon">
        <i class="fas fa-graduation-cap"></i>
      </div>
      <h2 class="welcome-title">Is this your first time as an editor?</h2>
      <p class="welcome-text">Follow this short tutorial on how to create a course and get started with our course management system.</p>
      <div class="welcome-buttons">
        <button class="welcome-btn welcome-btn-skip" onclick="skipTutorial()">Skip Tutorial</button>
        <button class="welcome-btn welcome-btn-start" onclick="startTutorial()">Start Tutorial</button>
      </div>
    </div>
  </div>

  <!-- Tutorial Click Indicator -->
  <div id="tutorialClickIndicator" class="tutorial-click-indicator" style="display: none;"></div>

  <!-- Add Course Modal -->
  <div id="addCourseModal" class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title">Create New Course</h2>
        <button class="modal-close" onclick="closeAddCourseModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form id="addCourseForm">
        <div class="form-group">
          <label for="courseName" class="form-label">Course Title *</label>
          <input type="text" id="courseName" class="form-control" placeholder="e.g., Complete Web Development Bootcamp" required>
        </div>

        <div class="form-group">
          <label for="courseDescription" class="form-label">Course Description *</label>
          <textarea id="courseDescription" class="form-control" rows="3" placeholder="Describe what students will learn..." required></textarea>
        </div>

        <div class="form-group">
          <label for="coursePrice" class="form-label">Course Price ($) *</label>
          <input type="number" id="coursePrice" class="form-control" placeholder="99.00" min="0" step="0.01" required>
        </div>

        <div class="form-group">
          <label for="courseCategory" class="form-label">Category *</label>
          <select id="courseCategory" class="form-control" required>
            <option value="">Select a category</option>
            <option value="finance">Finance</option>
            <option value="marketing">Marketing</option>
            <option value="personal-development">Personal Development</option>
            <option value="technology">Technology</option>
            <option value="business">Business</option>
            <option value="health">Health</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">Course Cover Image</label>
          <div class="image-upload-area" id="imageUploadArea">
            <div class="upload-icon">
              <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <p class="upload-text">Click to upload or drag and drop</p>
            <p class="upload-text" style="font-size: 0.875rem;">PNG, JPG up to 3MB. Recommended: 800x600px</p>
            <input type="file" id="courseImage" accept="image/*" style="display: none;">
          </div>
          <img id="imagePreview" class="image-preview">
          <input type="hidden" id="imageRecordId">
        </div>

        <button type="submit" class="btn-primary" id="createCourseBtn">
          <i class="fas fa-plus-circle"></i> Create Course
        </button>
      </form>
    </div>
  </div>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script>
    let coursesList = [];

    // Tutorial System Variables
    let currentTutorialStep = 0;
    let tutorialActive = false;
    let stepCompleted = false;

    const tutorialSteps = [
      {
        target: '.add-course-card',
        title: 'Create Your First Course',
        text: 'Welcome! Let\'s create your first course. Click on the "Add New Course" card to get started.',
        action: 'click',
        waitFor: 'modal-open'
      },
      {
        target: '#courseName',
        title: 'Enter Course Title',
        text: 'First, give your course a compelling title. This will be the first thing students see.',
        action: 'fill',
        waitFor: 'input-filled'
      },
      {
        target: '#courseDescription',
        title: 'Add Course Description',
        text: 'Now describe what students will learn in this course. Be specific and engaging!',
        action: 'fill',
        waitFor: 'input-filled'
      },
      {
        target: '#coursePrice',
        title: 'Set Course Price',
        text: 'Enter the price for your course. You can always change this later.',
        action: 'fill',
        waitFor: 'input-filled'
      },
      {
        target: '#courseCategory',
        title: 'Choose Category',
        text: 'Select the most appropriate category for your course to help students find it.',
        action: 'select',
        waitFor: 'option-selected'
      },
      {
        target: '#imageUploadArea',
        title: 'Add Course Cover Image',
        text: 'Upload an attractive cover image for your course. This will help attract students!',
        action: 'click',
        waitFor: 'image-uploaded'
      },
      {
        target: '#createCourseBtn',
        title: 'Create Your Course',
        text: 'Perfect! Now click "Create Course" to finish creating your first course.',
        action: 'click',
        waitFor: 'course-created'
      }
    ];

    // Tutorial Functions
    function showWelcomeModal() {
      const hasSeenTutorial = localStorage.getItem('courseEditorTutorialCompleted');
      if (!hasSeenTutorial) {
        $('#welcomeModal').addClass('show');
        document.body.style.overflow = 'hidden';
      }
    }

    function startTutorial() {
      $('#welcomeModal').removeClass('show');
      document.body.style.overflow = 'auto';

      tutorialActive = true;
      currentTutorialStep = 0;
      stepCompleted = false;

      showTutorialStep();
    }

    function skipTutorial() {
      localStorage.setItem('courseEditorTutorialCompleted', 'true');
      $('#welcomeModal').removeClass('show');
      hideTutorialElements();
      resetAllElements();
      tutorialActive = false;
      document.body.style.overflow = 'auto';
    }

    function dimOtherElements(exceptSelector) {
      // Dim all interactive elements except the target
      const elementsToCheck = [
        '.filter-btn',
        '.search-input',
        '.course-card',
        '.btn-action',
        '.add-course-card',
        '#courseName',
        '#courseDescription',
        '#coursePrice',
        '#courseCategory',
        '#createCourseBtn',
        '.modal-close',
        '.category-filters',
        '.controls'
      ];

      elementsToCheck.forEach(selector => {
        if (selector !== exceptSelector) {
          $(selector).addClass('tutorial-dimmed');
        }
      });
    }

    function resetAllElements() {
      $('.tutorial-dimmed').removeClass('tutorial-dimmed');
      $('.tutorial-active-element').removeClass('tutorial-active-element');
      $('#tutorialClickIndicator').hide();
    }

    function hideTutorialElements() {
      $('#tutorialMessage').removeClass('show');
      resetAllElements();
    }

    function showTutorialStep() {
      console.log('Showing tutorial step:', currentTutorialStep, 'of', tutorialSteps.length);

      if (currentTutorialStep >= tutorialSteps.length) {
        console.log('Tutorial completed, ending...');
        endTutorial();
        return;
      }

      const step = tutorialSteps[currentTutorialStep];
      console.log('Current step target:', step.target, 'title:', step.title);
      let targetElement = $(step.target);

      if (targetElement.length === 0) {
        console.log('Tutorial target not found:', step.target, 'Step:', currentTutorialStep);
        // Don't auto-advance, wait for element to appear
        setTimeout(() => {
          showTutorialStep();
        }, 500);
        return;
      }

      stepCompleted = false;

      // Reset all elements first
      resetAllElements();

      // Dim other elements
      dimOtherElements(step.target);

      // Highlight and zoom the target element
      highlightElement(targetElement);

      // Show floating message
      showFloatingMessage(step);

      // Add click indicator for click actions
      if (step.action === 'click') {
        showClickIndicator(targetElement);
      }

      // Update progress
      updateTutorialProgress();

      // Setup listeners
      setupStepListeners(step);

      // Scroll to element
      targetElement[0].scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'center'
      });
    }

    function highlightElement(targetElement) {
      targetElement.addClass('tutorial-active-element');
    }

    function showFloatingMessage(step) {
      $('#tutorialTitle').text(step.title);
      $('#tutorialText').text(step.text);
      $('#tutorialCounter').text(`Step ${currentTutorialStep + 1} of ${tutorialSteps.length}`);

      // Update action text
      let actionText = '';
      switch(step.action) {
        case 'click':
          actionText = '👆 Click to continue';
          break;
        case 'fill':
          actionText = '✏️ Type here';
          break;
        case 'select':
          actionText = '📋 Choose option';
          break;
        default:
          actionText = '✨ Complete this';
      }
      $('#tutorialAction').text(actionText);

      $('#tutorialMessage').addClass('show');
    }

    function showClickIndicator(targetElement) {
      const rect = targetElement[0].getBoundingClientRect();
      const indicator = $('#tutorialClickIndicator');

      // Calculate center position more accurately
      const centerX = rect.left + window.scrollX + (rect.width / 2);
      const centerY = rect.top + window.scrollY + (rect.height / 2);

      indicator.css({
        position: 'absolute',
        top: centerY - 30, // 30px is half the indicator height (60px)
        left: centerX - 30, // 30px is half the indicator width (60px)
        zIndex: 1001
      }).show();
    }

    function updateTutorialProgress() {
      const progress = ((currentTutorialStep + 1) / tutorialSteps.length) * 100;
      $('#tutorialProgressBar').css('width', progress + '%');
    }

    function applyFilters() {
      const category = $('.filter-btn.active').data('category');
      const term     = $('#searchInput').val().toLowerCase();
      let filtered   = coursesList.slice();
      if (category !== 'all') filtered = filtered.filter(c => c.category_course === category);
      if (term)          filtered = filtered.filter(c => c.name_course.toLowerCase().includes(term));
      renderGrid(filtered);
    }

    function renderGrid(list) {
      const container = $('#coursesGrid');
      container.empty();

      // Add existing courses first
      list.forEach(course => {
        const card = $(
          `<div class="course-card">
            <div class="course-image"><img src="${course.cover_img||'https://via.placeholder.com/300x180'}" alt="${course.name_course}"></div>
            <div class="course-content">
              <div class="course-title">${course.name_course}</div>
              <div class="course-description">${course.description_course}</div>
              <div class="course-actions" data-code="${course.code_course}"></div>
            </div>
          </div>`
        );
        container.append(card);
      });

      // Add "Add New Course" card at the end
      const addCourseCard = $(
        `<div class="add-course-card" onclick="openAddCourseModal()">
          <div class="add-course-icon">
            <i class="fas fa-plus-circle"></i>
          </div>
          <div class="add-course-title">Add New Course</div>
          <div class="add-course-subtitle">Click to create a new course</div>
        </div>`
      );
      container.append(addCourseCard);

      enhanceButtons();
    }

    // Reemplaza cada placeholder de .course-actions con el botón y URL dinámica
    function enhanceButtons() {
      $('.course-actions').each(function() {
        const code = $(this).data('code');
        const btn = $('<a class="btn-action"></a>')
          .text('Edit Course')
          .attr('href', `/lessons/?course=${code}`);
        $(this).html(btn);
      });
    }

    async function loadCourses() {
      $('#loading').show();
      try {
        const resp = await fetch('/wp-json/asg/v1/courses/api',{credentials:'include'});
        const json = await resp.json();
        coursesList = (json.success && Array.isArray(json.data)) ? json.data : [];
        renderGrid(coursesList);
      } catch (e) {
        $('#coursesGrid').html('<p style="color:#e53e3e;">Error loading courses.</p>');
      } finally {
        $('#loading').hide();
      }
    }

    function setupStepListeners(step) {
      // Remove previous listeners
      $(document).off('.tutorial');

      const target = $(step.target);

      switch(step.waitFor) {
        case 'modal-open':
          target.off('.tutorial').on('click.tutorial', function() {
            // Add completion animation
            showStepCompletion();
            setTimeout(() => {
              if ($('#addCourseModal').hasClass('show')) {
                stepCompleted = true;
                nextTutorialStep();
              }
            }, 800);
          });
          break;

        case 'input-filled':
          target.off('.tutorial').on('input.tutorial', function() {
            const value = $(this).val().trim();
            console.log('Input changed:', step.target, 'value:', value, 'length:', value.length);

            let isValid = false;

            // Special validation for price field
            if (step.target === '#coursePrice') {
              const numValue = parseFloat(value);
              isValid = !isNaN(numValue) && numValue > 0;
              console.log('Price validation:', numValue, 'isValid:', isValid);
            } else {
              // Regular text validation (minimum 3 characters)
              isValid = value.length > 2;
            }

            if (isValid && !stepCompleted) { // Only advance if not already completed
              console.log('Step completed for:', step.target);
              stepCompleted = true;
              showStepCompletion();

              // Remove the listener to prevent multiple triggers
              target.off('.tutorial');

              setTimeout(nextTutorialStep, 1500);
            } else if (!isValid) {
              stepCompleted = false;
              if (step.target === '#coursePrice') {
                $('#tutorialAction').text('💰 Enter a valid price...');
              } else {
                $('#tutorialAction').text('✏️ Keep typing...');
              }
            }
          });
          break;

        case 'option-selected':
          target.off('.tutorial').on('change.tutorial', function() {
            const value = $(this).val();
            console.log('Option changed:', step.target, 'value:', value);

            if (value !== '' && !stepCompleted) { // Only advance if not already completed
              console.log('Step completed for:', step.target);
              stepCompleted = true;
              showStepCompletion();

              // Remove the listener to prevent multiple triggers
              target.off('.tutorial');

              setTimeout(nextTutorialStep, 1500);
            } else if (value === '') {
              stepCompleted = false;
              $('#tutorialAction').text('📋 Choose option');
            }
          });
          break;

        case 'image-uploaded':
          // Listen for image upload completion
          target.off('.tutorial').on('click.tutorial', function() {
            console.log('Image upload area clicked');
            $('#tutorialAction').text('📸 Upload an image...');

            // Listen for successful image upload
            const checkImageUpload = setInterval(() => {
              const preview = $('#imagePreview');
              if (preview.is(':visible') && preview.attr('src')) {
                console.log('Image uploaded successfully');
                clearInterval(checkImageUpload);
                stepCompleted = true;
                showStepCompletion();
                setTimeout(nextTutorialStep, 1500);
              }
            }, 500);

            // Timeout after 30 seconds
            setTimeout(() => {
              clearInterval(checkImageUpload);
              if (!stepCompleted) {
                console.log('Image upload timeout, allowing to continue');
                stepCompleted = true;
                $('#tutorialAction').text('⏭️ Skip image (optional)');
                showStepCompletion();
                setTimeout(nextTutorialStep, 2000);
              }
            }, 30000);
          });
          break;

        case 'course-created':
          // Validate all fields are filled before allowing course creation
          target.off('.tutorial').on('click.tutorial', function(e) {
            const courseName = $('#courseName').val().trim();
            const courseDescription = $('#courseDescription').val().trim();
            const coursePrice = $('#coursePrice').val().trim();
            const courseCategory = $('#courseCategory').val();

            console.log('Validating form:', {courseName, courseDescription, coursePrice, courseCategory});

            if (!courseName || !courseDescription || !coursePrice || !courseCategory) {
              e.preventDefault();
              e.stopPropagation();
              $('#tutorialAction').text('❌ Fill all required fields first!');

              // Reset after 2 seconds
              setTimeout(() => {
                $('#tutorialAction').text('👆 Click to continue');
              }, 2000);

              return false;
            }

            // Validate price is a number
            const priceNum = parseFloat(coursePrice);
            if (isNaN(priceNum) || priceNum <= 0) {
              e.preventDefault();
              e.stopPropagation();
              $('#tutorialAction').text('❌ Enter a valid price!');

              setTimeout(() => {
                $('#tutorialAction').text('👆 Click to continue');
              }, 2000);

              return false;
            }

            // All fields are filled, show creating message
            console.log('All validations passed, creating course...');
            $('#tutorialAction').text('⏳ Creating course...');
          });
          break;
      }
    }

    function showStepCompletion() {
      // Update message to show completion
      $('#tutorialAction').text('✅ Completed!');

      // Add success animation to active element
      const activeElement = $('.tutorial-active-element');
      activeElement.css('animation', 'none');
      setTimeout(() => {
        activeElement.css('animation', 'tutorialGlow 0.5s ease-in-out 2');
      }, 50);

      // Hide click indicator
      $('#tutorialClickIndicator').fadeOut(300);
    }

    function nextTutorialStep() {
      // Only advance if step is actually completed
      if (!stepCompleted) {
        console.log('Step not completed, not advancing. Current step:', currentTutorialStep);
        return;
      }

      // Prevent multiple rapid advances
      if (window.tutorialAdvancing) {
        console.log('Tutorial already advancing, ignoring duplicate call');
        return;
      }

      window.tutorialAdvancing = true;
      console.log('Advancing from step', currentTutorialStep, 'to', currentTutorialStep + 1);

      currentTutorialStep++;
      stepCompleted = false; // Reset for next step

      // Add a smooth transition
      $('#tutorialMessage').removeClass('show');

      setTimeout(() => {
        window.tutorialAdvancing = false;
        showTutorialStep();
      }, 300);
    }

    function endTutorial() {
      localStorage.setItem('courseEditorTutorialCompleted', 'true');

      // Hide tutorial elements
      hideTutorialElements();
      resetAllElements();

      tutorialActive = false;

      // Remove all tutorial listeners
      $(document).off('.tutorial');
      $('*').off('.tutorial');

      // Wait a moment for the course to appear in the grid, then highlight it
      setTimeout(() => {
        highlightNewCourse();
      }, 1000);
    }

    function highlightNewCourse() {
      // Find the first course card (the newly created one should be first)
      const newCourseCard = $('.course-card').first();

      if (newCourseCard.length > 0) {
        // Scroll to the new course
        newCourseCard[0].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Add special highlight for the new course
        newCourseCard.addClass('tutorial-active-element');

        // Show congratulations message pointing to the new course
        showCongratulationsMessage(newCourseCard);

        // Remove highlight after 5 seconds
        setTimeout(() => {
          newCourseCard.removeClass('tutorial-active-element');
          $('#congratulationsMessage').fadeOut(500, function() {
            $(this).remove();
          });
        }, 5000);
      }
    }

    function showCongratulationsMessage(targetElement) {
      const rect = targetElement[0].getBoundingClientRect();

      const congratsHtml = `
        <div id="congratulationsMessage" style="
          position: absolute;
          top: ${rect.top + window.scrollY - 80}px;
          left: ${rect.left + window.scrollX + (rect.width / 2) - 160}px;
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
          padding: 1rem 1.5rem;
          border-radius: 16px;
          box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
          z-index: 2000;
          width: 320px;
          text-align: center;
          animation: slideUp 0.6s ease-out;
          border: 1px solid rgba(255, 255, 255, 0.2);
        ">
          <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎉</div>
          <h3 style="margin: 0 0 0.5rem 0; font-size: 1.1rem; font-weight: 700;">Congratulations!</h3>
          <p style="margin: 0; font-size: 0.9rem; opacity: 0.95;">
            You've successfully created your first course! This is your new course ready for students.
          </p>
        </div>
      `;

      $('body').append(congratsHtml);
    }

    function restartTutorial() {
      localStorage.removeItem('courseEditorTutorialCompleted');

      if ($('#addCourseModal').hasClass('show')) {
        closeAddCourseModal();
      }

      tutorialActive = false;
      currentTutorialStep = 0;
      stepCompleted = false;

      hideTutorialElements();
      resetAllElements();
      $(document).off('.tutorial');
      $('*').off('.tutorial');

      setTimeout(() => {
        showWelcomeModal();
      }, 500);
    }

    // Emergency function to completely clean tutorial state
    function cleanTutorial() {
      tutorialActive = false;
      currentTutorialStep = 0;
      stepCompleted = false;

      hideTutorialElements();
      resetAllElements();
      $(document).off('.tutorial');
      $('*').off('.tutorial');

      $('#welcomeModal').removeClass('show');
      $('#congratulationsMessage').remove();
      document.body.style.overflow = 'auto';

      console.log('Tutorial cleaned successfully');
    }

    window.restartTutorial = restartTutorial;
    window.cleanTutorial = cleanTutorial;

    // Global variables
    let uploadedImageId = null;

    // Modal functions
    function openAddCourseModal() {
      $('#addCourseModal').addClass('show');
      document.body.style.overflow = 'hidden';
      setupImageUpload();
    }

    function closeAddCourseModal() {
      $('#addCourseModal').removeClass('show');
      document.body.style.overflow = 'auto';
      $('#addCourseForm')[0].reset();
      resetImageUpload();
    }

    // Image upload functions
    function setupImageUpload() {
      const uploadArea = document.getElementById('imageUploadArea');
      const fileInput = document.getElementById('courseImage');
      const preview = document.getElementById('imagePreview');

      // Click to upload
      uploadArea.addEventListener('click', () => fileInput.click());

      // File input change
      fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
          handleImageUpload(e.target.files[0]);
        }
      });

      // Drag and drop
      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
      });

      uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
      });

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
          handleImageUpload(files[0]);
        }
      });
    }

    function resetImageUpload() {
      uploadedImageId = null;
      document.getElementById('imageRecordId').value = '';
      document.getElementById('imagePreview').style.display = 'none';
      document.getElementById('imageUploadArea').innerHTML = `
        <div class="upload-icon">
          <i class="fas fa-cloud-upload-alt"></i>
        </div>
        <p class="upload-text">Click to upload or drag and drop</p>
        <p class="upload-text" style="font-size: 0.875rem;">PNG, JPG up to 3MB. Recommended: 800x600px</p>
      `;
    }

    // Image upload function
    async function handleImageUpload(file) {
      // Validate file
      if (!file.type.startsWith('image/')) {
        alert('Please select a valid image file');
        return;
      }

      if (file.size > 3 * 1024 * 1024) { // 3MB
        alert('Image size must be less than 3MB');
        return;
      }

      try {
        // Show loading state
        const uploadArea = document.getElementById('imageUploadArea');
        uploadArea.innerHTML = `
          <div class="upload-icon">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p class="upload-text">Uploading image...</p>
        `;

        // Upload image
        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', 'course_image');

        const response = await fetch('/wp-json/asg/v1/media/api', {
          method: 'POST',
          headers: {
            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
          },
          body: formData
        });

        const result = await response.json();

        if (result.success && result.data) {
          uploadedImageId = result.data.image_id || result.data.id;
          document.getElementById('imageRecordId').value = uploadedImageId;

          // Show preview
          const preview = document.getElementById('imagePreview');
          preview.src = result.data.url || result.data.medium_url;
          preview.style.display = 'block';

          // Update upload area
          uploadArea.innerHTML = `
            <div class="upload-icon">
              <i class="fas fa-check-circle" style="color: #10b981;"></i>
            </div>
            <p class="upload-text" style="color: #10b981;">Image uploaded successfully!</p>
            <p class="upload-text" style="font-size: 0.875rem;">Click to change image</p>
          `;

          console.log('✅ Image uploaded successfully:', uploadedImageId);
        } else {
          throw new Error(result.message || 'Upload failed');
        }
      } catch (error) {
        console.error('Image upload error:', error);
        alert('Error uploading image: ' + error.message);

        // Reset upload area
        resetImageUpload();
      }
    }

    // Create course function
    async function createCourse(courseData) {
      try {
        const response = await fetch('/wp-json/asg/v1/courses/api', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-WP-Nonce': '<?php echo wp_create_nonce('wp_rest'); ?>'
          },
          body: JSON.stringify(courseData),
          credentials: 'include'
        });

        const result = await response.json();

        if (result.success) {
          closeAddCourseModal();

          // Check if tutorial is active and this is the last step
          if (tutorialActive && currentTutorialStep === tutorialSteps.length - 1) {
            stepCompleted = true;

            // Show completion in the tutorial message
            $('#tutorialAction').text('✅ Course Created!');
            showStepCompletion();

            // End tutorial after a delay
            setTimeout(() => {
              endTutorial();
            }, 2000);
          } else {
            // Show success message for normal users
            alert('✅ Course created successfully!');
          }

          // Reload courses
          loadCourses();
        } else {
          throw new Error(result.message || 'Failed to create course');
        }
      } catch (error) {
        console.error('Error creating course:', error);
        alert('❌ Error creating course: ' + error.message);
      }
    }

    $(document).ready(() => {
      loadCourses();
      $('.category-filters').on('click','.filter-btn',function(){
        $('.filter-btn').removeClass('active');$(this).addClass('active');applyFilters();
      });
      $('#searchInput').on('input', applyFilters);

      // Initialize tutorial after a short delay and ensure page is ready
      setTimeout(() => {
        // Clean any existing tutorial state first
        cleanTutorial();
        // Then show welcome modal if needed
        showWelcomeModal();
      }, 1500);

      // Handle form submission
      $('#addCourseForm').on('submit', function(e) {
        e.preventDefault();

        // Get image URL from preview
        const imagePreview = document.getElementById('imagePreview');
        const coverImgUrl = imagePreview.style.display !== 'none' ? imagePreview.src : '';

        const courseData = {
          name_course: $('#courseName').val().trim(),
          description_course: $('#courseDescription').val().trim(),
          price_course: parseFloat($('#coursePrice').val()) || 0,
          category_course: $('#courseCategory').val(),
          status_course: 'published',
          language_course: 'en',
          duration_course: 0,
          cover_img: coverImgUrl,
          image_record_id: uploadedImageId
        };

        // Basic validation
        if (!courseData.name_course || !courseData.description_course || !courseData.category_course) {
          alert('Please fill in all required fields');
          return;
        }


        // Disable button and show loading
        const btn = $('#createCourseBtn');
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Creating...');

        createCourse(courseData).finally(() => {
          btn.prop('disabled', false).html('<i class="fas fa-plus-circle"></i> Create Course');
        });
      });

      // Close modal when clicking outside
      $('#addCourseModal').on('click', function(e) {
        if (e.target === this) {
          closeAddCourseModal();
        }
      });

      // Tutorial event listeners
      $(document).on('keydown', function(e) {
        if (tutorialActive && e.key === 'Escape') {
          e.preventDefault();
          skipTutorial();
        }
      });

      // Handle window resize during tutorial
      $(window).on('resize', function() {
        if (tutorialActive && currentTutorialStep < tutorialSteps.length) {
          const step = tutorialSteps[currentTutorialStep];
          const targetElement = $(step.target);
          if (targetElement.length > 0) {
            // Reposition tutorial elements
            highlightElement(targetElement);
            showFloatingMessage(step);
            if (step.action === 'click') {
              showClickIndicator(targetElement);
            }
          }
        }
      });

      // Prevent clicks on dimmed elements during tutorial
      $(document).on('click', '.tutorial-dimmed', function(e) {
        if (tutorialActive) {
          e.preventDefault();
          e.stopPropagation();

          // Gentle shake animation to indicate it's not clickable
          $(this).css('animation', 'shake 0.5s ease-in-out');
          setTimeout(() => {
            $(this).css('animation', '');
          }, 500);

          // Highlight the current tutorial target
          const step = tutorialSteps[currentTutorialStep];
          if (step && step.target) {
            const target = $(step.target);
            if (target.length > 0) {
              target[0].scrollIntoView({ behavior: 'smooth', block: 'center' });

              // Add attention animation
              target.css('animation', 'none');
              setTimeout(() => {
                target.css('animation', 'tutorialGlow 1s ease-in-out 3');
              }, 50);
            }
          }

          return false;
        }
      });

      // Close welcome modal when clicking outside
      $('#welcomeModal').on('click', function(e) {
        if (e.target === this) {
          skipTutorial();
        }
      });
    });
  </script>
</body>
</html>
